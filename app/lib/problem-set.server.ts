import { ProblemSet } from '~/models'

export async function fetchProblems(): Promise<string[]> {
  const response = await fetch('api.axiia.ai/v1/problem-catalog/problems')
  const data = await response.json()
  return data.problems
}

export async function createProblemSet(
  problemIds: string[],
  expireMinutes?: number,
): Promise<string> {
  // First create in external API to get the ID
  const response = await fetch('api.axiia.ai/v1/test-management/problemsets', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ problemIds, expireMinutes }),
  })

  const { problemSetId } = await response.json()

  // Store in database
  await ProblemSet.create({
    id: problemSetId,
    problemIds,
  })

  return problemSetId
}

export async function fetchAllProblemSets() {
  return await ProblemSet.findAll({
    order: [['createdAt', 'DESC']],
  })
}
