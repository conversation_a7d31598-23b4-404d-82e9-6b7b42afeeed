import { Test, User } from '~/models'

export async function createTest(userId: string, problemSetId: string) {
  // Create test in external API
  const response = await fetch('api.axiia.ai/v1/test-management/tests', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ problemSetId }),
  });

  const { testId, url } = await response.json();

  await Test.create({
    id: testId,
    problemSetId,
    userId,
    url,
  })

  return { testId, url }
}

export async function getUserTests(userId: string) {
  return Test.findAll({
    where: { userId },
    order: [['createdAt', 'DESC']],
  })
}

export async function deleteUserTests(email: string): Promise<number> {
  try {
    const user = await User.findOne({
      where: { email }
    });

    if (!user) {
      return 0;
    }

    const result = await Test.destroy({
      where: { userId: user.id }
    });

    return result;
  } catch (error) {
    console.error('Error deleting user tests:', error);
    throw new Error('Failed to delete user tests');
  }
} 